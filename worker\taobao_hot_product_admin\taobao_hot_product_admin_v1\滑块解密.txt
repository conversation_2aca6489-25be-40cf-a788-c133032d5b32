[小椰好物集] 采集并保存商品: 7/13
发送api请求的完整数据: {"pageNum":34,"pageSize":30,"filedKey":"itemRankListInfo","rankType":"1","topnItemList":null,"recommendTabType":0,"cateIds":"50010788,126762001","featured":"isHotItem=是","priceSelect":"50_999999","commissionRateSelect":"1_100","soldQuantity365Select":"10_999999","code":"1"}
商品采集失败，返回结果: {
  success: false,
  error: 'FAIL_SYS_USER_VALIDATE: RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试',
  data: [],
  response: Response {
    size: 0,
    [Symbol(Body internals)]: {
      body: [PassThrough],
      stream: [PassThrough],
      boundary: null,
      disturbed: true,
      error: null
    },
    [Symbol(Response internals)]: {
      type: 'default',
      url: 'https://h5api.m.taobao.com/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0/?jsv=2.7.0&appKey=12574478&t=1754381337419&sign=814c33346f19db7b7ecfd487267cc47a&api=mtop.taobao.cic.downlink.newstation.itemcenter.item.query&v=1.0&valueType=original&preventFallback=true&type=originaljson&dataType=json&data=%7B%22pageNum%22%3A34%2C%22pageSize%22%3A30%2C%22filedKey%22%3A%22itemRankListInfo%22%2C%22rankType%22%3A%221%22%2C%22topnItemList%22%3Anull%2C%22recommendTabType%22%3A0%2C%22cateIds%22%3A%2250010788%2C126762001%22%2C%22featured%22%3A%22isHotItem%3D%E6%98%AF%22%2C%22priceSelect%22%3A%2250_999999%22%2C%22commissionRateSelect%22%3A%221_100%22%2C%22soldQuantity365Select%22%3A%2210_999999%22%2C%22code%22%3A%221%22%7D',
      status: 200,
      statusText: '',
      headers: [Object],
      counter: 0,
      highWaterMark: 16384
    }
  },
  originalCount: 0,
  filteredCount: 0,
  end: true
}

如果遇到FAIL_SYS_USER_VALIDATE这种情况，那么响应的header中肯定有set-cookie，取出x5secdata=xgb7b2fbb39094c423ja45851a1a5850b033b187e8c9a3ca4cf21754387423a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0这个url
拼接成https://h5api.m.taobao.com//h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0/_____tmd_____/punish?x5secdata=xgb7b2fbb39094c423ja45851a1a5850b033b187e8c9a3ca4cf21754387423a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0&x5step=2&action=captcha&pureCaptcha=


set-cookie
x5secdata=xgb7b2fbb39094c423ja45851a1a5850b033b187e8c9a3ca4cf21754387423a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0; Max-Age=20; Expires=Tue, 05-Aug-2025 09:50:43 GMT; Domain=taobao.com; Path=/;SameSite=None;Secure
set-cookie
x5sectag=178214; Max-Age=60; Expires=Tue, 05-Aug-2025 09:51:23 GMT; Path=/


步骤2：以下是py代码，用于解密captcha的，转换为node代码，并优化代码，将第一步拼接完的代码放到url参数中进行请求
def get_captcha_config(captcha_url,session):
    '''
    获取验证码配置信息
    :param captcha_url: url
    :return:
    '''
    response = session.get(captcha_url,verify=False)
    if re.findall(r'window._config_ = (\{.*?\});', response.text, re.S):
        config = re.findall(r'window._config_ = (\{.*?\});', response.text, re.S)[0]
    else:
        logger.info(f"验证码配置错误：{response.text}")
        raise Exception
    config = json.loads(config)
    logger.info(f"获取验证码配置信息config：{config}")
    return config

success = 0
fail = 0
def get_x5sec(url,session):
    global success,fail
    captcha_config: dict = get_captcha_config(url, session)
    logger.info(f"当前验证码类型:{captcha_config['action']}")
    if captcha_config['action'] != 'captcha':
        return
    data = {
        'captcha_config': captcha_config,
        'key':'KnBx5v9p6Xpp8LBh6hnQPjlV1lR8iNmn9R5w3vKBqfnWyOPpXv4qHGgMrZOB8bD3BCOAvKghieLRS1L9ooBCpaVGq8k6Ij4XmYNLD2RWdB5owqpQRt2jVg96EdexOQWf',
    }
    rp = requests.post('http://220.168.146.21:9107/tb_n', json=data)
    # print(rp.text)
    DATA = rp.json()
    url = DATA['urlEncode']
    # logger.info(url)
    session.headers['bx_et'] = DATA['bx_et']
    session.headers['bx-pp'] = DATA['bx_pp']
    session.headers['referer'] = DATA['referer']
    x5sec_response = session.get(url,verify=False)
    print(x5sec_response.text)
    if 'x5sec' in x5sec_response.cookies:
        logger.success(x5sec_response.cookies)
        success+=1
    else:
        logger.warning(x5sec_response)
        fail+=1
    logger.info(f'当前成功率:{round((success/(success+fail))*100,2)} 次数:{(success+fail)}')
for i in range(1):
    session = requests.Session()
    session.proxies = None
    ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    session.headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'bx-v': '2.5.3',
        'cache-control': 'no-cache',
        'content-type': 'application/x-www-form-urlencoded',
        'pragma': 'no-cache',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': ua,
    }
    url = ''
    print(f"使用测试URL: {url}")
    get_x5sec(url=url,session=session)

步骤3： 步骤2执行完成，获取到的响应内容是以下内容，获取到x5sec的值，修改该主播的cookie，如果cookie中有x5sec这个值，则修改，如果没这个值，则添加进去，然后将新的cookie继续进行采集商品。
2025-08-05 17:57:28.621 | INFO     | __main__:get_captcha_config:28 - 获取验证码配置信息config：{'renderTo': '#nocaptcha', 'NCTOKENSTR': 'cc6579329b7311456e0f976bd7c35a1f', 'action': 'captcha', 'HOST': 'h5api.m.taobao.com:443', 'PATH': '/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0', 'FORMACTIOIN': '/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0/_____tmd_____/verify/', 'BXSTEP': '100', 'SECDATA': 'xg7c169082ebe5ec8cjacc6579329b7311456e0f976bd7c35a1f1754387836a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0', 'NCAPPKEY': 'X82Y__81f18d132d5de91b15e80976dfc49410', 'isUpgrade': 'false', 'customImage': '', 'logo': '', 'logoLink': '', 'copyright': '', 'noCaptchaCallback': '', 'language': '', 'languageConfig': '', 'url': '/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0/_____tmd_____/punish?x5secdata=xg7c169082ebe5ec8cjacc6579329b7311456e0f976bd7c35a1f1754387836a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0', 'languageConfigJson': '', 'noCaptchaLanguage': '', 'js': '', 'css': '', 'crossSite': '0', 'qrcode': 'zGV5MptzEUVuD5dr18NaHw|aJHVfA|3mXONw_0|', 'pp': {'enc': '73b9b1fe095cf82569dd37887398451ab636408c3958820c0189d7724c27a57d4fd062779b0b4a744e6411a09672bdcb', 'f': 'md5', 'i': 0, 'k': 'qweasdzxcg', 'l': 0, 'mt': 3000, 'q': 'a', 't': '1754387848401'}, 'captchaConfigInfo': '', 'downGradeHttp': False, 'nonce': '2bc3bccc6007934e'}
2025-08-05 17:57:28.621 | INFO     | __main__:get_x5sec:36 - 当前验证码类型:captcha
{"code":0,"dt":"success","ec":200,"result":{"code":0,"sig":"from bx"},"success":true}
2025-08-05 17:57:28.851 | SUCCESS  | __main__:get_x5sec:54 - <RequestsCookieJar[<Cookie x5sec=7b22733b32223a2261633737393731366231353532323631222c22617365727665723b33223a22307c4350797178385147454c2b47786f6b474b4941454d4c65636c2f50392f2f2f2f2f77453d227d for h5api.m.taobao.com/>]>
2025-08-05 17:57:28.851 | INFO     | __main__:get_x5sec:59 - 当前成功率:100.0 次数:1

注意：写一个新的js文件进行处理这个解密过程，每一步都要打印日志，在采集页面也要打印日志信息