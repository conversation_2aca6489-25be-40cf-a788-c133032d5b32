import { CaptchaSolver } from './src/captcha-solver.js';

/**
 * 滑块解密功能测试
 */
async function testCaptchaSolver() {
    console.log('🧪 开始测试滑块解密功能...');
    
    const solver = new CaptchaSolver();
    
    // 测试1: 检测是否需要滑块解密
    console.log('\n📋 测试1: 错误检测功能');
    
    const normalResponse = {
        success: true,
        error: null
    };
    
    const captchaResponse = {
        success: false,
        error: 'FAIL_SYS_USER_VALIDATE: RGV587_ERROR::SM::哎哟喂,被挤爆啦,请稍后重试'
    };
    
    const otherErrorResponse = {
        success: false,
        error: 'FAIL_SYS_TOKEN_EXOIRED'
    };
    
    console.log('正常响应需要解密:', solver.needsCaptchaSolving(normalResponse));
    console.log('验证码错误需要解密:', solver.needsCaptchaSolving(captchaResponse));
    console.log('其他错误需要解密:', solver.needsCaptchaSolving(otherErrorResponse));
    
    // 测试2: URL构建功能
    console.log('\n📋 测试2: URL构建功能');
    
    const originalUrl = 'https://h5api.m.taobao.com/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0/?jsv=2.7.0&appKey=12574478';
    const x5secdata = 'xgb7b2fbb39094c423ja45851a1a5850b033b187e8c9a3ca4cf21754387423a-717315356a-563753417abaac3en2215566863489a0__bx__h5api.m.taobao.com:443/h5/mtop.taobao.cic.downlink.newstation.itemcenter.item.query/1.0';
    
    try {
        const captchaUrl = solver.buildCaptchaUrl(originalUrl, x5secdata);
        console.log('✅ URL构建成功');
        console.log('验证码URL:', captchaUrl);
    } catch (error) {
        console.error('❌ URL构建失败:', error);
    }
    
    // 测试3: Cookie更新功能
    console.log('\n📋 测试3: Cookie更新功能');
    
    const originalCookie = '_m_h5_tk=abc123; _m_h5_tk_enc=def456; other=value';
    const x5secValue = '7b22733b32223a2261633737393731366231353532323631222c22617365727665723b33223a22307c4350797178385147454c2b47786f6b474b4941454d4c65636c2f50392f2f2f2f2f77453d227d';
    
    try {
        const updatedCookie = solver.updateCookieWithX5Sec(originalCookie, x5secValue);
        console.log('✅ Cookie更新成功');
        console.log('原始Cookie:', originalCookie);
        console.log('更新后Cookie:', updatedCookie);
    } catch (error) {
        console.error('❌ Cookie更新失败:', error);
    }
    
    // 测试4: 统计信息功能
    console.log('\n📋 测试4: 统计信息功能');
    
    console.log('初始统计:', solver.getStats());
    
    // 模拟一些成功和失败
    solver.successCount = 5;
    solver.failCount = 2;
    
    console.log('模拟统计:', solver.getStats());
    
    solver.resetStats();
    console.log('重置后统计:', solver.getStats());
    
    console.log('\n🎉 滑块解密功能测试完成');
}

// 运行测试
testCaptchaSolver().catch(console.error);
