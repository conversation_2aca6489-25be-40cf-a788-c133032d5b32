


CREATE TABLE anchors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,
    anchor_id TEXT UNIQUE NOT NULL,
    anchor_cookie TEXT,
    password TEXT,
    sort  INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active',
    total_orders INTEGER DEFAULT 0,
    total_amount REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_anchors_anchor_id ON anchors(anchor_id);
CREATE INDEX idx_anchors_created_at ON anchors(created_at);
CREATE INDEX idx_anchors_status ON anchors(status);






CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    anchor_name TEXT NOT NULL,                  
    product_id TEXT NOT NULL,                    
    product_title TEXT,                
    product_price DECIMAL(10,2)  NOT NULL,             
    commission_rate DECIMAL(5,2),               
    commission_amount DECIMAL(10,2),           
    product_source TEXT,                       
    alliance_channel TEXT,                      
    product_category TEXT,                       
    sales_365_days INTEGER DEFAULT 0,
    sales_30_days INTEGER DEFAULT 0,
    sales_7_days INTEGER DEFAULT 0,
    sales_7_days_growth_rate DECIMAL(5,2),
    orders_30_days INTEGER DEFAULT 0,
    anchor_sales_30_days DECIMAL(10,2) DEFAULT 0,
    tag INTEGER,
    date TEXT,
    product_type TEXT,   
    shop_name TEXT,                            
    safe_shop_id TEXT,                         
    feature_tags TEXT,
    batch_number TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE INDEX idx_products_anchor_name ON products(anchor_name);
CREATE INDEX idx_products_product_id ON products(product_id);
CREATE INDEX idx_products_product_source ON products(product_source);
CREATE INDEX idx_products_alliance_channel ON products(alliance_channel);
CREATE INDEX idx_products_batch_number ON products(batch_number);
CREATE INDEX idx_products_created_at ON products(created_at);
CREATE INDEX idx_products_sales_7_days_growth_rate ON products(sales_7_days_growth_rate);
CREATE INDEX idx_products_anchor_sales_30_days ON products(anchor_sales_30_days);
CREATE INDEX idx_products_shop_name ON products(shop_name);
CREATE INDEX idx_products_safe_shop_id ON products(safe_shop_id);


CREATE INDEX idx_products_anchor_batch ON products(anchor_name, batch_number);
CREATE UNIQUE INDEX idx_products_unique_anchor_product_batch ON products(anchor_name, product_id, batch_number);



CREATE TABLE selection_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,                   
    description TEXT,                        
    total_target INTEGER DEFAULT 500,         
    rule_groups TEXT NOT NULL,                 
    status TEXT DEFAULT 'active',             
    created_by TEXT,                           
    last_used_at DATETIME,                   
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE INDEX idx_selection_rules_status ON selection_rules(status);
CREATE INDEX idx_selection_rules_created_at ON selection_rules(created_at);
CREATE INDEX idx_selection_rules_last_used_at ON selection_rules(last_used_at);


CREATE TABLE selection_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id INTEGER NOT NULL,                  
    rule_name TEXT NOT NULL,                  
    execution_time DATETIME NOT NULL,         
    total_selected INTEGER DEFAULT 0,         
    execution_result TEXT,                     
    batch_number TEXT,                        
    status TEXT DEFAULT 'completed',           
    error_message TEXT,                        
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES selection_rules(id)
);


CREATE INDEX idx_selection_executions_rule_id ON selection_executions(rule_id);
CREATE INDEX idx_selection_executions_execution_time ON selection_executions(execution_time);
CREATE INDEX idx_selection_executions_batch_number ON selection_executions(batch_number);

