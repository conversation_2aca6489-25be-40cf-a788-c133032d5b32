<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品采集 - 淘宝热销产品管理系统</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-1303094100.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 20px;
            overflow: hidden;
        }

        .nav-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 1px solid #e2e8f0;
            padding: 0 2rem;
        }

        .page-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .content-layout {
            padding: 2rem;
        }

        .filters-section {
            margin-bottom: 2rem;
        }

        .condition-data-container {
            margin-top: 1.5rem;
            margin-bottom: 2rem;
        }

        .condition-data-content {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }

        .condition-json {
            margin: 0;
            padding: 0.75rem 1rem;
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: nowrap;
            overflow-x: auto;
            word-break: break-all;
        }

        .condition-json .json-key {
            color: #7dd3fc;
        }

        .condition-json .json-string {
            color: #86efac;
        }

        .condition-json .json-number {
            color: #fbbf24;
        }

        .condition-json .json-boolean {
            color: #f472b6;
        }

        .condition-json .json-null {
            color: #94a3b8;
        }

        .settings-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .settings-left {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            flex: 1;
        }

        .settings-left h4 {
            margin: 0;
        }

        .settings-controls {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-shrink: 0;
            align-items: flex-end;
            margin-top: 0;
        }

        .filter-group .action-buttons {
            margin-top: 1.5rem;
        }

        @media (max-width: 768px) {
            .settings-row {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }

            .action-buttons {
                justify-content: center;
                margin-top: 0;
            }
        }

        .bottom-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 1.5rem;
            min-height: 600px;
        }

        @media (max-width: 1200px) {
            .bottom-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        .nav-links {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--gray-600);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: var(--primary-color);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1.25rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-decoration: none;
            font-size: 0.8125rem;
            min-height: 36px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(107, 114, 128, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
        }

        .btn-outline {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            background-color: transparent;
        }

        .btn-outline:hover {
            color: white;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .form-group {
            margin-bottom: 0.75rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.25rem;
            font-size: 0.75rem;
        }

        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--gray-200);
            border-radius: 6px;
            font-size: 0.75rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .filter-button {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.375rem 0.75rem;
            margin: 0.125rem;
            border: 1px solid var(--gray-200);
            border-radius: 6px;
            background: white;
            color: var(--gray-700);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .filter-button.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }

        .anchor-button {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            color: var(--gray-700);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .anchor-button:hover {
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .anchor-button.active {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            border-color: var(--success-color);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
        }

        .log-container {
            height: calc(100vh - 200px);
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0.5rem;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            width: 100%;
            box-sizing: border-box;
        }

        .log-entry {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            font-size: 0.875rem;
            line-height: 1.5;
            border-left: 4px solid transparent;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
        }

        .log-entry.log-product {
            background: #ffffff;
            border: 1px solid #10b981;
            border-left: 4px solid #10b981;
            box-shadow: 0 1px 3px rgba(16, 185, 129, 0.1);
            width: 100%;
            box-sizing: border-box;
        }

        .log-info {
            background: #eff6ff;
            color: #1e40af;
            border-left-color: #3b82f6;
        }

        .log-success {
            background: #f0fdf4;
            color: #166534;
            border-left-color: #10b981;
        }

        .log-error {
            background: #fef2f2;
            color: #dc2626;
            border-left-color: #ef4444;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-600);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            padding: 1.5rem 0;
            justify-content: center;
            margin-top: 1rem;
        }

        .settings-controls {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
        }

        .settings-controls .form-group {
            min-width: 150px;
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .range-input {
            width: 90px;
            flex-shrink: 0;
        }

        .range-separator {
            color: var(--gray-500);
            font-weight: 500;
            font-size: 0.875rem;
        }

        .range-unit {
            color: var(--gray-600);
            font-size: 0.75rem;
            font-weight: 500;
            min-width: 20px;
            white-space: nowrap;
        }

        .filter-section {
            margin-bottom: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .filter-section:last-child {
            border-bottom: none;
        }

        .settings-section {
            border-bottom: none;
            padding-bottom: 0;
        }

        .filter-section h4 {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            width: 120px;
            flex-shrink: 0;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            flex: 1;
        }

        .filter-row {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;
        }

        .filter-row h4 {
            margin: 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            min-width: 80px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 筛选组容器 */
        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-height: 40px;
        }

        .filter-group h4 {
            white-space: nowrap;
            flex-shrink: 0;
            min-width: 70px;
        }

        .filters-grid {
            display: block;
        }

        /* Tag按钮样式 */
        .tag-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.375rem;
            flex: 1;
        }

        .tag-btn {
            padding: 0.375rem 0.75rem;
            border: 1px solid #e2e8f0;
            background: white;
            color: var(--gray-600);
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .tag-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: #eff6ff;
        }

        .tag-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .tag-btn.active:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .table-wrapper {
            max-height: 900px;
            overflow-y: auto;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f1f5f9;
            color: var(--gray-600);
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .table-wrapper::-webkit-scrollbar {
            width: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 店铺列表输入框样式 */
        #shopKeywords {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.75rem;
            line-height: 1.4;
        }

        #shopKeywords::placeholder {
            color: #9ca3af;
            font-family: inherit;
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- 导航栏 -->
        <nav class="nav-container">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">
                        <i class="fas fa-shopping-cart text-blue-500 mr-2"></i>
                        淘宝热销产品管理系统
                    </h1>
                </div>
                <div class="nav-links">
                    <a href="products.html" class="nav-link">
                        <i class="fas fa-box"></i>产品管理
                    </a>
                    <a href="selection-rules.html" class="nav-link">
                        <i class="fas fa-filter"></i>选品规则
                    </a>
                    <a href="rule-management.html" class="nav-link">
                        <i class="fas fa-cogs"></i>规则管理
                    </a>
                    <a href="anchors.html" class="nav-link">
                        <i class="fas fa-microphone"></i>主播管理
                    </a>
                    <a href="product-collection.html" class="nav-link active">
                        <i class="fas fa-shopping-cart"></i>商品采集
                    </a>
                </div>
            </div>
        </nav>

        <!-- 页面标题区域 -->
        <div class="page-header">
            <div class="relative z-10">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold mb-2">商品采集</h2>
                        <p class="text-blue-100 text-lg">根据筛选条件采集淘宝商品数据，支持实时预览和批量采集</p>
                    </div>

                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="content-layout">
            <!-- 上方：筛选条件 -->
            <div class="filters-section">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-filter text-blue-500"></i>
                            筛选条件
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- 主播选择 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-microphone text-green-500"></i>选择主播</h4>
                                    <div id="anchorButtons" class="filter-buttons">
                                        <!-- 主播按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商品类目 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-tags text-purple-500"></i>商品类目</h4>
                                    <div id="categoryFilters" class="filter-buttons">
                                        <!-- 类目按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商品标签 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-star text-yellow-500"></i>商品标签</h4>
                                    <div id="featuredFilters" class="filter-buttons">
                                        <!-- 标签按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商品价格、佣金比例、365天销量、店铺类型 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-dollar-sign text-green-500"></i>商品价格</h4>
                                    <div class="range-inputs">
                                        <input type="number" id="priceMin" class="form-control range-input" value="10" placeholder="最低价格" min="0">
                                        <span class="range-separator">-</span>
                                        <input type="number" id="priceMax" class="form-control range-input" placeholder="最高价格" min="0">
                                        <span class="range-unit">元</span>
                                    </div>
                                </div>

                                <div class="filter-group">
                                    <h4><i class="fas fa-percentage text-orange-500"></i>佣金比例</h4>
                                    <div class="range-inputs">
                                        <input type="number" id="commissionMin" class="form-control range-input" value="1" placeholder="最低佣金" min="0" max="100" step="0.1">
                                        <span class="range-separator">-</span>
                                        <input type="number" id="commissionMax" class="form-control range-input" placeholder="最高佣金" min="0" max="100" step="0.1">
                                        <span class="range-unit">%</span>
                                    </div>
                                </div>

                                <div class="filter-group">
                                    <h4><i class="fas fa-chart-line text-blue-500"></i>365天销量</h4>
                                    <div class="range-inputs">
                                        <input type="number" id="salesMin" class="form-control range-input" value="10" placeholder="最低销量" min="0">
                                        <span class="range-separator">-</span>
                                        <input type="number" id="salesMax" class="form-control range-input" placeholder="最高销量" min="0">
                                        <span class="range-unit">件</span>
                                    </div>
                                </div>

                                <div class="filter-group">
                                    <h4><i class="fas fa-store text-indigo-500"></i>店铺类型</h4>
                                    <div id="shopTypeFilters" class="filter-buttons">
                                        <!-- 店铺类型按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 店铺等级 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-crown text-yellow-500"></i>店铺等级</h4>
                                    <div id="shopLevelFilters" class="filter-buttons">
                                        <!-- 店铺等级按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商品渠道 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-route text-pink-500"></i>商品渠道</h4>
                                    <div id="icTagsFilters" class="filter-buttons">
                                        <!-- 商品渠道按钮将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 采集设置 -->
                        <div class="filter-section">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <h4><i class="fas fa-cog text-gray-500"></i>采集设置</h4>
                                    <div class="range-inputs">
                                        <span class="range-unit">请求间隔（秒）</span>
                                        <input type="number" id="intervalInput" class="form-control range-input" value="3" min="1" max="60">
                                        <span class="range-unit">每页数量</span>
                                        <select id="pageSizeSelect" class="form-control range-input">
                                            <option value="30">30条</option>
                                            <option value="50">50条</option>
                                            <option value="100">100条</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="filter-group">
                                    <h4><i class="fas fa-tag text-red-500"></i>商品标签 <span style="color: red;">*</span></h4>
                                    <div id="productTagButtons" class="tag-buttons">
                                        <button type="button" class="tag-btn" data-tag="1">类目品</button>
                                        <button type="button" class="tag-btn" data-tag="2">流量品</button>
                                        <button type="button" class="tag-btn" data-tag="3">店铺品</button>
                                        <button type="button" class="tag-btn" data-tag="4">出单品</button>
                                        <button type="button" class="tag-btn" data-tag="5">点击品</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 佣金金额范围、店铺列表和操作按钮 -->
                        <div class="filter-section settings-section">
                            <div class="filter-row" style="justify-content: space-between;">
                                <div style="display: flex; gap: 2rem; align-items: center; flex: 1;">
                                    <div class="filter-group">
                                        <h4><i class="fas fa-yen-sign text-orange-500"></i>佣金金额</h4>
                                        <div class="range-inputs">
                                            <input type="number" id="commissionRangeMin" value="10" class="form-control range-input" placeholder="最低佣金金额" min="0" max="9999" step="0.01">
                                            <span class="range-separator">-</span>
                                            <input type="number" id="commissionRangeMax" class="form-control range-input" placeholder="最高佣金金额" min="0" max="9999" step="0.01">
                                            <span class="range-unit">元</span>
                                        </div>
                                    </div>

                                    <div class="filter-group">
                                        <h4><i class="fas fa-store text-purple-500"></i>店铺列表</h4>
                                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                            <textarea id="shopKeywords" class="form-control" placeholder="一行一个店铺名&#10;例如：&#10;小米官方旗舰店&#10;华为官方旗舰店" rows="3" style="width: 200px; resize: vertical;"></textarea>
                                            <span class="range-unit" style="font-size: 0.7rem; color: #6b7280;">留空则不按店铺筛选</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="action-buttons" style="margin-top: 0;">
                                    <button id="startCollectionBtn" class="btn btn-success">
                                        <i class="fas fa-play"></i>开始采集
                                    </button>
                                    <button id="pauseCollectionBtn" class="btn btn-warning" disabled>
                                        <i class="fas fa-pause"></i>暂停采集
                                    </button>
                                    <button id="stopCollectionBtn" class="btn btn-secondary" disabled>
                                        <i class="fas fa-stop"></i>停止采集
                                    </button>
                                    <button id="clearLogBtn" class="btn btn-secondary">
                                        <i class="fas fa-trash"></i>清空日志
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>






                <!-- 条件数据显示 -->
                <div id="conditionDataContainer" class="condition-data-container" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-code text-purple-500"></i>
                                筛选条件数据
                                <button id="toggleConditionBtn" class="btn btn-sm btn-outline ml-2">
                                    <i class="fas fa-eye"></i>显示/隐藏
                                </button>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="condition-data-content">
                                <pre id="conditionDataDisplay" class="condition-json"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下方：数据预览和日志 -->
            <div class="bottom-grid">
                <!-- 左侧：数据预览 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-table text-green-500"></i>
                            数据预览
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- 统计信息 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="currentPage">0</div>
                                <div class="stat-label">当前页</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="collectedCount">0</div>
                                <div class="stat-label">已采集</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="captchaCount">0</div>
                                <div class="stat-label">解密次数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="collectionStatus">待开始</div>
                                <div class="stat-label">状态</div>
                            </div>
                        </div>

                        <!-- 商品表格 -->
                        <div class="table-wrapper">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>商品ID</th>
                                        <th>商品名称</th>
                                        <th>价格</th>
                                        <th>佣金</th>
                                        <th>30天销量</th>
                                        <th>365天销量</th>
                                        <th>店铺名称</th>
                                    </tr>
                                </thead>
                                <tbody id="productTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center text-gray-500 py-8">
                                            <i class="fas fa-inbox text-4xl mb-2 block"></i>
                                            暂无数据，请开始采集
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 右侧：实时日志 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-terminal text-gray-500"></i>
                            实时日志
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container">
                            <div class="log-entry log-info">系统已就绪，等待开始采集...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/product-collection.js"></script>
</body>
</html>
